@font-face {
  font-family: "Century Gothic";
  src: url("../fonts/century-gothic-regular.woff2") format("woff2"),
    url("../fonts/century-gothic-regular.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Century Gothic";
  src: url("../fonts/century-gothic-semibold.woff") format("woff2"),
    url("../fonts/century-gothic-semibold.woff") format("woff");
  font-weight: 600;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: "Century Gothic";
  src: url("../fonts/century-gothic-bold.woff") format("woff2"),
    url("../fonts/century-gothic-bold.woff") format("woff");
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

.scroll-to-top {
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease-in-out;
}

.scroll-to-top.visible {
    opacity: 1;
    pointer-events: auto;
}

header.scrolled {
    transition: all 0.3s ease;
}

header.scrolled .widian-header-logo {
    padding: 1rem 0 !important;
}

header.scrolled .widian-header-logo img {
    width: 160px !important;
    transition: all 0.3s ease;
}

/* ======= PRODUCT Card style ======= */
.product-card {
  border-bottom: 1px solid #cccccc;
  border-right: 1px solid #cccccc;
}

/* ======= BASE (1 column) ======= */
.product-grid > div:first-child {
  border-top: 1px solid #cccccc;
  border-left: 1px solid #cccccc;
}

/* ======= LG: 4 columns ======= */
@media (min-width: 1024px) {
  .product-grid > div {
    border-right: 1px solid #cccccc;
  }

  .product-grid > div:nth-child(4n) {
    border-right: 1px solid #cccccc;
  }

  .product-grid > div:nth-child(-n + 4) {
    border-top: 1px solid #cccccc !important;
  }

  .product-grid > div:nth-child(4n + 1) {
    /* border-left: 1px solid #cccccc; */
  }

  .product-grid.product-grid-3 > div:nth-child(4) {
    border-top: none!important;
    /* border-right: none; */
    border-right: 1px solid #cccccc;
    border-left: 1px solid #cccccc;
  }
}

/* ======= MD: 3 columns ======= */
@media (max-width: 1024px) {
  .product-grid > div {
    border-right: 1px solid #cccccc;
  }
  .product-grid > div:nth-child(3n + 1) {
    border-left: 1px solid #cccccc;
  }

  /* 3rd item of odd row (3, 9, 15, ...) */
  .product-grid > div:nth-child(6n + 3) {
    border-left: none;
  }

  /* 2nd item of even row (5, 11, 17, ...) */
  .product-grid > div:nth-child(6n - 1) {
    border-left: none;
  }
  /* .product-grid > div:nth-child(3n) {
      border-right: none;
    } */
  .product-grid > div:nth-child(-n + 3) {
    border-top: 1px solid #cccccc;
  }

  /* .product-grid > div:nth-child(4) {
      border-top: none;
    } */
}

/* ======= SM: 2 columns ======= */
@media (max-width: 768px) {
  .product-grid > div:nth-child(2n + 1) {
    /* border-left: 1px solid #cccccc; */
  }
  .product-grid > div:nth-child(3) {
    border-top: 0px solid #cccccc;
    border-left: 1px solid #cccccc;
  }
  .product-grid > div:nth-child(6n + 4) {
    border-left: none;
  }
  .product-grid > div:nth-child(-n + 2) {
    border-top: 1px solid #cccccc;
  }
}

.shadow-cart {
  box-shadow: 0px -1px 5px 0px rgba(61, 60, 58, 0.19);
}

.shadow-policy {
  box-shadow: 0px 0px 10px 0px rgba(61, 60, 58, 0.10);
}

.owl-carousel .owl-dots {
  position: absolute;
  bottom: 0px;
  left: 50%;
  transform: translateX(-50%);
}

.owl-carousel .owl-nav {
  position: absolute;
  width: 100%;
  display: flex;
  justify-content: space-between;
  top: 50%;
  align-items: center;
  padding: 0 16px;
  transform: translateY(-50%);
}

.complementary-product-carousel .owl-nav {
  top: -50px !important;
  width: auto !important;
  right: -4px !important;
  justify-content: start !important;
  padding: 0 !important;
  transform: translateY(0) !important;
}

.owl-carousel .owl-nav button:hover {
  background-color: transparent !important;
}

.product-carousel .owl-nav {
  width: 104% !important;
  left: -2%;
  padding: 0 !important;
  transform: translateY(0) !important;
}

.static-container {
  color: #666666;
  font-size: 1rem;
  line-height: 1.75rem;
  font-family: 'Century Gothic';
}
.static-container h2 {
  font-family: 'Playfair Display';
  font-size: 1.75rem;
  line-height: 2rem;
  color: rgb(117 124 91);
  font-weight: 500;
  margin-bottom: 1rem;
}
.static-container p {
  color: #666666;
  font-size: 1rem;
  line-height: 1.75rem;
  margin-bottom: 1rem;
}
.static-container h3 {
  color: #3D3C3A;
  font-size: 1.25rem;
  margin-bottom: 0.7rem;
  font-weight: 600;
  font-family: 'Century Gothic';
}
.static-container ul {
  margin-bottom: 1.5rem;
  margin-left: 1.5rem;
}
.static-container ul li {
  list-style: disc;
  font-size: 1rem;
  margin-bottom: 0.5rem;
}
.static-container table {
  font-size: 0.875rem;
  line-height: 1.25rem;
  text-align: left;
  color: rgb(61 60 58);
  background-color: rgb(253 249 242);
  border-width: 1px;
  margin-bottom: 1rem;
}
.static-container table th {
  padding-top: 1rem;
  padding-bottom: 1rem;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  font-weight: 600;
  border-width: 1px;
  border-color: rgb(239 236 226);
}
.static-container table td {
  padding-top: 1rem;
  padding-bottom: 1rem;
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  font-weight: 400;
  border-width: 1px;
  border-color: rgb(239 236 226);
}

.widian-acc-text p {
  font-size: 0.85rem;
  line-height: 1.3rem;
  margin-bottom: 0.5rem;
}
.widian-acc-text p:last-child {
  margin-bottom: 0;
}

/*PROFILE PAGE*/
.profile-display {
  box-shadow: 0 0 10px #3c3b391a;
}
.profile-menu {
  box-shadow: 0 0 10px #3c3b391a;
}
.profile-page .profile-data {
  box-shadow: 0 0 10px #3c3b391a;
}
